{% extends 'base_new.html' %}

{% block title %}GSO Dashboard - MSRRMS{% endblock %}

{% block page_title %}GSO Dashboard{% endblock %}
{% block mobile_title %}GSO Dashboard{% endblock %}

{% block content %}
<div x-data="gsoDashboard()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">GSO Dashboard</h1>
                <p class="text-blue-100 mt-1">Manage supply requests and inventory</p>
            </div>
            <div class="hidden md:flex items-center space-x-4">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_requests }}</div>
                    <div class="text-blue-200 text-sm">Total Requests</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Pending Requests Card -->
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 border-yellow-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending</div>
                        <div class="text-2xl font-bold text-gray-900">{{ pending_requests }}</div>
                        <div class="text-xs text-gray-500 mt-1">Awaiting approval</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approved Requests Card -->
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 border-blue-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="text-sm font-medium text-gray-500 uppercase tracking-wide">Approved</div>
                        <div class="text-2xl font-bold text-gray-900">{{ approved_requests }}</div>
                        <div class="text-xs text-gray-500 mt-1">Ready for release</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Released Requests Card -->
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 border-green-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="text-sm font-medium text-gray-500 uppercase tracking-wide">Released</div>
                        <div class="text-2xl font-bold text-gray-900">{{ released_requests }}</div>
                        <div class="text-xs text-gray-500 mt-1">Completed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rejected Requests Card -->
        <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4 border-red-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="text-sm font-medium text-gray-500 uppercase tracking-wide">Rejected</div>
                        <div class="text-2xl font-bold text-gray-900">{{ rejected_requests }}</div>
                        <div class="text-xs text-gray-500 mt-1">Declined</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
        <!-- Batch Requests -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-indigo-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-100 rounded-md flex items-center justify-center">
                        <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-sm font-medium text-gray-500">Batch Requests</div>
                    <div class="text-lg font-semibold text-gray-900">{{ batch_requests }}</div>
                    <div class="text-xs text-indigo-600">{{ pending_batch_requests }} pending</div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-sm font-medium text-gray-500">Active Users</div>
                    <div class="text-lg font-semibold text-gray-900">{{ active_users }}</div>
                    <div class="text-xs text-green-600">{{ total_users }} total</div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-sm font-medium text-gray-500">Low Stock</div>
                    <div class="text-lg font-semibold text-gray-900">{{ low_stock_items }}</div>
                    <div class="text-xs text-red-600">Items below minimum</div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-sm font-medium text-gray-500">This Week</div>
                    <div class="text-lg font-semibold text-gray-900">{{ recent_requests }}</div>
                    <div class="text-xs text-purple-600">New requests</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Release Management Quick Actions -->
    <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-xl shadow-lg border border-green-200 p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-4 lg:mb-0">
                <h3 class="text-lg font-semibold text-green-900 mb-2">Release Management</h3>
                <p class="text-sm text-green-700">Quick access to release operations and statistics</p>
            </div>

            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Ready for Release -->
                <div class="bg-white rounded-lg p-3 shadow-sm border border-green-200">
                    <div class="text-center">
                        <div class="text-xl font-bold text-green-600">{{ ready_for_release }}</div>
                        <div class="text-xs text-green-700">Ready to Release</div>
                    </div>
                </div>

                <!-- Partial Release Available -->
                <div class="bg-white rounded-lg p-3 shadow-sm border border-yellow-200">
                    <div class="text-center">
                        <div class="text-xl font-bold text-yellow-600">{{ partial_release_available }}</div>
                        <div class="text-xs text-yellow-700">Partial Available</div>
                    </div>
                </div>

                <!-- Today's Releases -->
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-200">
                    <div class="text-center">
                        <div class="text-xl font-bold text-blue-600">{{ today_releases }}</div>
                        <div class="text-xs text-blue-700">Released Today</div>
                    </div>
                </div>

                <!-- Recent Releases -->
                <div class="bg-white rounded-lg p-3 shadow-sm border border-purple-200">
                    <div class="text-center">
                        <div class="text-xl font-bold text-purple-600">{{ recent_releases }}</div>
                        <div class="text-xs text-purple-700">This Week</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4 flex flex-wrap gap-3">
            <a href="{% url 'supply:release_management' %}"
               class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-medium text-white hover:bg-green-700 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Release Management
            </a>

            <a href="{% url 'supply:release_history' %}"
               class="inline-flex items-center px-4 py-2 bg-white border border-green-300 rounded-lg font-medium text-green-700 hover:bg-green-50 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Release History
            </a>

            {% if ready_for_release > 0 %}
            <span class="inline-flex items-center px-3 py-2 bg-green-100 border border-green-300 rounded-lg text-sm font-medium text-green-800">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                {{ ready_for_release }} request{{ ready_for_release|pluralize }} ready for immediate release
            </span>
            {% endif %}
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100">
        <div class="px-6 py-5">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Filter Requests</h3>
                <div class="flex items-center space-x-2">
                    <div id="loading-indicator" class="htmx-indicator">
                        <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <form hx-get="{% url 'supply:gso_dashboard' %}"
                  hx-target="#request-list"
                  hx-trigger="change, submit"
                  hx-indicator="#loading-indicator"
                  class="grid grid-cols-1 md:grid-cols-4 gap-4">

                <!-- Status Filter -->
                <div class="space-y-2">
                    <label for="status" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Status
                    </label>
                    <select name="status" id="status"
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        <option value="" {% if not status_filter %}selected{% endif %}>All Statuses</option>
                        {% for status_val, status_display in status_choices %}
                        <option value="{{ status_val }}" {% if status_val == status_filter %}selected{% endif %}>{{ status_display }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Department Filter -->
                <div class="space-y-2">
                    <label for="department" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Department
                    </label>
                    <select name="department" id="department"
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        <option value="" {% if not department_filter %}selected{% endif %}>All Departments</option>
                        {% for department in departments %}
                        <option value="{{ department }}" {% if department == department_filter %}selected{% endif %}>{{ department }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Search -->
                <div class="space-y-2">
                    <label for="search" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Search
                    </label>
                    <input type="text" name="search" id="search" value="{{ search_query }}"
                           placeholder="Search requests..."
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                </div>

                <!-- Sort -->
                <div class="space-y-2">
                    <label for="sort" class="block text-sm font-medium text-gray-700">
                        <svg class="inline h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                        </svg>
                        Sort By
                    </label>
                    <select name="sort" id="sort"
                           class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                        <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>Newest First</option>
                        <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Oldest First</option>
                        <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item A-Z</option>
                        <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Z-A</option>
                        <option value="department" {% if sort_by == 'department' %}selected{% endif %}>Department A-Z</option>
                        <option value="-department" {% if sort_by == '-department' %}selected{% endif %}>Department Z-A</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Batch Operations -->
    <div class="bg-white shadow rounded-lg mb-6" x-show="selectedRequests.length > 0" x-cloak>
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    <span x-text="selectedRequests.length"></span> request(s) selected
                </h3>
                <div class="flex space-x-2">
                    <button @click="bulkApprove()" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                        Approve
                    </button>
                    <button @click="bulkReject()" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">
                        Reject
                    </button>
                    <button @click="clearSelection()" class="px-4 py-2 bg-gray-200 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-300">
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Request List -->
    <div id="request-list" hx-get="{% url 'supply:gso_dashboard' %}" hx-trigger="load, refresh from:body" hx-swap="innerHTML">
        <!-- Loading indicator -->
        <div id="loading-indicator" class="htmx-indicator text-center py-4">
            <p>Loading requests...</p>
        </div>
        <!-- Initial content will be loaded by HTMX -->
    </div>

    <!-- Bulk Approve Modal -->
    <div x-show="showBulkApproveModal" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showBulkApproveModal" 
                 x-transition:enter="ease-out duration-300" 
                 x-transition:enter-start="opacity-0" 
                 x-transition:enter-end="opacity-100" 
                 x-transition:leave="ease-in duration-200" 
                 x-transition:leave-start="opacity-100" 
                 x-transition:leave-end="opacity-0" 
                 class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
                 aria-hidden="true"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div x-show="showBulkApproveModal" 
                 @click.away="showBulkApproveModal = false" 
                 x-transition:enter="ease-out duration-300" 
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" 
                 x-transition:leave="ease-in duration-200" 
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
                 class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Approve Requests
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Are you sure you want to approve the <span x-text="selectedRequests.length"></span> selected requests?
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="confirmBulkApprove()" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Approve
                    </button>
                    <button @click="showBulkApproveModal = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Reject Modal -->
    <div x-show="showBulkRejectModal" class="fixed z-10 inset-0 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            <div class="bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Reject Requests</h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">
                            You are about to reject <span x-text="selectedRequests.length"></span> request(s). Please provide a reason.
                        </p>
                        <textarea x-model="rejectionReason" rows="3" class="mt-2 shadow-sm focus:ring-red-500 focus:border-red-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Reason for rejection..."></textarea>
                    </div>
                </div>
                <div class="flex justify-center space-x-4 px-4 py-3">
                    <button @click="confirmBulkReject()" 
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Confirm
                    </button>
                    <button @click="showBulkRejectModal = false" 
                            class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function gsoDashboard() {
    return {
        selectedRequests: [],
        showBulkApproveModal: false,
        showBulkRejectModal: false,
        rejectionReason: '',
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll(checked) {
            if (checked) {
                // Select all visible requests
                const checkboxes = document.querySelectorAll('input[name="request_checkbox"]');
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            } else {
                this.selectedRequests = [];
            }
        },
        
        isSelected(requestId) {
            return this.selectedRequests.includes(requestId);
        },
        
        clearSelection() {
            this.selectedRequests = [];
        },
        
        bulkApprove() {
            this.showBulkApproveModal = true;
        },
        
        bulkReject() {
            this.showBulkRejectModal = true;
            this.rejectionReason = '';
        },
        
        confirmBulkApprove() {
            this.performBulkAction('bulk_approve');
            this.showBulkApproveModal = false;
        },
        
        confirmBulkReject() {
            this.performBulkAction('bulk_reject');
            this.showBulkRejectModal = false;
        },
        
        performBulkAction(action) {
            const formData = new FormData();
            formData.append('action', action);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            
            if (action === 'bulk_reject') {
                formData.append('rejection_reason', this.rejectionReason);
            }
            
            this.selectedRequests.forEach(id => {
                formData.append('selected_requests', id);
            });
            
            fetch('{% url "supply:batch_operations" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => {
                if (response.ok) {
                    // Show success notification
                    const actionText = action === 'bulk_approve' ? 'approved' : 'rejected';
                    showNotification('success', 'Batch Operation Complete', `Successfully ${actionText} ${this.selectedRequests.length} request(s).`);

                    // Refresh the request list
                    htmx.trigger('#request-list', 'refresh');
                    this.clearSelection();
                } else {
                    throw new Error('Server error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Operation Failed', 'An error occurred while processing the batch operation.');
            });
        }
    }
}
</script>
{% endblock %}