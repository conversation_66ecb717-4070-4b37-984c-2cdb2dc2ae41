from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid


class SupplyCategory(models.Model):
    """Model for organizing supply items into categories"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    color_code = models.CharField(max_length=7, default='#3B82F6', help_text="Hex color code for visual distinction")
    icon_class = models.CharField(max_length=50, default='fas fa-box', help_text="Font Awesome icon class")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Supply Category"
        verbose_name_plural = "Supply Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def item_count(self):
        """Get the number of items in this category"""
        return self.items.count()

    @property
    def total_value(self):
        """Get the total value of items in this category"""
        return sum(item.current_stock * (item.unit_cost or 0) for item in self.items.all())

    @property
    def low_stock_count(self):
        """Get the number of low stock items in this category"""
        return self.items.filter(current_stock__lte=models.F('minimum_stock')).count()


class UserProfile(models.Model):
    """Extended user profile with role and department information"""
    ROLE_CHOICES = [
        ('DEPARTMENT', 'Department Staff'),
        ('GSO', 'GSO Staff')
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100)
    phone = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
    
    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class SupplyItem(models.Model):
    """Supply items available in inventory"""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(SupplyCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='items')
    unit = models.CharField(max_length=50, help_text="e.g., pieces, boxes, reams, etc.")
    current_stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    minimum_stock = models.IntegerField(default=10, validators=[MinValueValidator(0)])
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    supplier = models.CharField(max_length=200, blank=True)
    location = models.CharField(max_length=100, blank=True, help_text="Storage location")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.current_stock} {self.unit})"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum stock level"""
        return self.current_stock <= self.minimum_stock
    
    def can_fulfill_quantity(self, quantity):
        """Check if there's enough stock to fulfill a request"""
        return self.current_stock >= quantity
    
    class Meta:
        verbose_name = "Supply Item"
        verbose_name_plural = "Supply Items"
        ordering = ['name']


class SupplyRequest(models.Model):
    """Supply requests submitted by departments - can contain multiple items"""
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('RELEASED', 'Released')
    ]

    request_id = models.CharField(max_length=20, unique=True, editable=False)
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='supply_requests')
    department = models.CharField(max_length=100)
    purpose = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    is_batch_request = models.BooleanField(default=False)

    # Legacy fields for backward compatibility (single item requests)
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, null=True, blank=True)
    quantity = models.IntegerField(validators=[MinValueValidator(1)], null=True, blank=True)
    
    # Approval fields
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='approved_requests'
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    approval_remarks = models.TextField(blank=True)
    
    # Release fields
    released_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='released_requests'
    )
    released_at = models.DateTimeField(null=True, blank=True)
    release_remarks = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def save(self, *args, **kwargs):
        if not self.request_id:
            # Generate unique request ID
            self.request_id = f"REQ-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Set department from requester's profile if not set
        if not self.department and hasattr(self.requester, 'userprofile'):
            self.department = self.requester.userprofile.department
            
        super().save(*args, **kwargs)
    
    def approve(self, approved_by, remarks=""):
        """Approve the request"""
        if self.status == 'PENDING' and self.item.can_fulfill_quantity(self.quantity):
            self.status = 'APPROVED'
            self.approved_by = approved_by
            self.approved_at = timezone.now()
            self.approval_remarks = remarks
            self.save()
            return True
        return False
    
    def reject(self, rejected_by, remarks=""):
        """Reject the request"""
        if self.status == 'PENDING':
            self.status = 'REJECTED'
            self.approved_by = rejected_by
            self.approved_at = timezone.now()
            self.approval_remarks = remarks
            self.save()
            return True
        return False
    
    def can_be_released(self):
        """Check if this request can be released (has sufficient stock)"""
        if self.status != 'APPROVED':
            return False

        if self.is_batch_request:
            # For batch requests, check all items
            if not self.request_items.exists():
                return False
            return all(
                item.item.can_fulfill_quantity(item.approved_quantity or item.quantity)
                for item in self.request_items.all()
            )
        else:
            # For single item requests
            return (self.item and self.quantity and
                   self.item.can_fulfill_quantity(self.quantity))

    def get_stock_status(self):
        """Get detailed stock status information for display"""
        if self.is_batch_request:
            if not self.request_items.exists():
                return {
                    'available': False,
                    'message': 'No items in batch',
                    'details': {'total_items': 0, 'available_items': 0, 'item_details': []}
                }

            total_items = self.request_items.count()
            available_items = 0
            item_details = []

            for request_item in self.request_items.all():
                quantity_needed = request_item.approved_quantity or request_item.quantity
                can_fulfill = request_item.item.can_fulfill_quantity(quantity_needed)
                if can_fulfill:
                    available_items += 1

                item_details.append({
                    'item_name': request_item.item.name,
                    'requested': quantity_needed,
                    'available': request_item.item.current_stock,
                    'can_fulfill': can_fulfill,
                    'shortage': max(0, quantity_needed - request_item.item.current_stock),
                    'unit': request_item.item.unit
                })

            if available_items == total_items:
                return {
                    'available': True,
                    'message': f'All {total_items} items available',
                    'details': {
                        'total_items': total_items,
                        'available_items': available_items,
                        'item_details': item_details
                    }
                }
            elif available_items == 0:
                return {
                    'available': False,
                    'message': f'No items available (0/{total_items})',
                    'details': {
                        'total_items': total_items,
                        'available_items': available_items,
                        'item_details': item_details
                    }
                }
            else:
                return {
                    'available': False,
                    'message': f'Partial stock ({available_items}/{total_items} items)',
                    'details': {
                        'total_items': total_items,
                        'available_items': available_items,
                        'item_details': item_details
                    }
                }
        else:
            if not self.item or not self.quantity:
                return {
                    'available': False,
                    'message': 'Invalid request',
                    'details': {'requested': 0, 'available': 0, 'can_fulfill': False}
                }

            can_fulfill = self.item.can_fulfill_quantity(self.quantity)
            if can_fulfill:
                return {
                    'available': True,
                    'message': f'{self.item.current_stock} {self.item.unit} available',
                    'details': {
                        'requested': self.quantity,
                        'available': self.item.current_stock,
                        'can_fulfill': True,
                        'unit': self.item.unit
                    }
                }
            else:
                return {
                    'available': False,
                    'message': f'Only {self.item.current_stock} {self.item.unit} available',
                    'details': {
                        'requested': self.quantity,
                        'available': self.item.current_stock,
                        'can_fulfill': False,
                        'shortage': self.quantity - self.item.current_stock,
                        'unit': self.item.unit
                    }
                }

    def get_release_progress(self):
        """Get release progress information for tracking"""
        if self.status != 'RELEASED':
            return {
                'is_released': False,
                'progress_percentage': 0,
                'released_items': 0,
                'total_items': 1 if not self.is_batch_request else self.request_items.count()
            }

        if not self.is_batch_request:
            return {
                'is_released': True,
                'progress_percentage': 100,
                'released_items': 1,
                'total_items': 1,
                'released_at': self.released_at,
                'released_by': self.released_by.get_full_name() if self.released_by else 'Unknown'
            }

        total_items = self.request_items.count()
        released_items = self.request_items.filter(released_quantity__isnull=False).count()
        progress_percentage = (released_items / total_items * 100) if total_items > 0 else 0

        return {
            'is_released': True,
            'progress_percentage': progress_percentage,
            'released_items': released_items,
            'total_items': total_items,
            'released_at': self.released_at,
            'released_by': self.released_by.get_full_name() if self.released_by else 'Unknown'
        }

    def can_partial_release(self):
        """Check if request supports partial release (batch requests only)"""
        if not self.is_batch_request or self.status != 'APPROVED':
            return False

        # Check if at least one item can be released
        return any(
            item.item.can_fulfill_quantity(item.approved_quantity or item.quantity)
            for item in self.request_items.all()
        )

    def release(self, released_by, remarks=""):
        """Release the approved request and update inventory"""
        if not self.can_be_released():
            return False

        if self.is_batch_request:
            # Release batch request
            for request_item in self.request_items.all():
                quantity_to_release = request_item.approved_quantity or request_item.quantity

                # Update inventory
                request_item.item.current_stock -= quantity_to_release
                request_item.item.save()

                # Create inventory transaction
                InventoryTransaction.objects.create(
                    item=request_item.item,
                    transaction_type='OUT',
                    quantity=quantity_to_release,
                    reference_request=self,
                    performed_by=released_by,
                    remarks=f"Released for batch request {self.request_id} - {request_item.item.name}"
                )
        else:
            # Release single item request
            # Update inventory
            self.item.current_stock -= self.quantity
            self.item.save()

            # Create inventory transaction
            InventoryTransaction.objects.create(
                item=self.item,
                transaction_type='OUT',
                quantity=self.quantity,
                reference_request=self,
                performed_by=released_by,
                remarks=f"Released for request {self.request_id}"
            )

        # Update request status
        self.status = 'RELEASED'
        self.released_by = released_by
        self.released_at = timezone.now()
        self.release_remarks = remarks
        self.save()

        return True

    def release_with_tracking(self, released_by, remarks="", partial_release=False):
        """Enhanced release method with detailed tracking and validation"""
        from django.db import transaction

        if not self.can_be_released() and not (partial_release and self.can_partial_release()):
            return {
                'success': False,
                'message': 'Request cannot be released due to insufficient stock',
                'details': self.get_stock_status()
            }

        released_items = []
        failed_items = []

        try:
            with transaction.atomic():
                if self.is_batch_request:
                    # Release batch request
                    for request_item in self.request_items.all():
                        quantity_to_release = request_item.approved_quantity or request_item.quantity

                        # Check if this specific item can be released
                        if request_item.item.can_fulfill_quantity(quantity_to_release):
                            # Update inventory
                            request_item.item.current_stock -= quantity_to_release
                            request_item.item.save()

                            # Mark item as released
                            request_item.released_quantity = quantity_to_release
                            request_item.save()

                            # Create inventory transaction
                            InventoryTransaction.objects.create(
                                item=request_item.item,
                                transaction_type='OUT',
                                quantity=quantity_to_release,
                                reference_request=self,
                                performed_by=released_by,
                                remarks=f"Released for batch request {self.request_id} - {request_item.item.name}"
                            )

                            released_items.append({
                                'item': request_item.item.name,
                                'quantity': quantity_to_release,
                                'unit': request_item.item.unit
                            })
                        else:
                            failed_items.append({
                                'item': request_item.item.name,
                                'requested': quantity_to_release,
                                'available': request_item.item.current_stock,
                                'unit': request_item.item.unit
                            })

                            if not partial_release:
                                return {
                                    'success': False,
                                    'message': f'Insufficient stock for {request_item.item.name}',
                                    'details': {
                                        'failed_item': request_item.item.name,
                                        'requested': quantity_to_release,
                                        'available': request_item.item.current_stock
                                    }
                                }
                else:
                    # Release single item request
                    if self.item.can_fulfill_quantity(self.quantity):
                        # Update inventory
                        self.item.current_stock -= self.quantity
                        self.item.save()

                        # Create inventory transaction
                        InventoryTransaction.objects.create(
                            item=self.item,
                            transaction_type='OUT',
                            quantity=self.quantity,
                            reference_request=self,
                            performed_by=released_by,
                            remarks=f"Released for request {self.request_id}"
                        )

                        released_items.append({
                            'item': self.item.name,
                            'quantity': self.quantity,
                            'unit': self.item.unit
                        })
                    else:
                        return {
                            'success': False,
                            'message': f'Insufficient stock for {self.item.name}',
                            'details': {
                                'requested': self.quantity,
                                'available': self.item.current_stock
                            }
                        }

                # Update request status
                self.status = 'RELEASED'
                self.released_by = released_by
                self.released_at = timezone.now()
                self.release_remarks = remarks
                self.save()

                # Create audit log
                AuditLog.objects.create(
                    user=released_by,
                    action='RELEASE',
                    model_name='SupplyRequest',
                    object_id=self.id,
                    details=f"Released request {self.request_id} - {len(released_items)} items released"
                )

                return {
                    'success': True,
                    'message': f'Successfully released {len(released_items)} items',
                    'details': {
                        'released_items': released_items,
                        'failed_items': failed_items,
                        'partial_release': len(failed_items) > 0,
                        'released_at': self.released_at,
                        'released_by': released_by.get_full_name()
                    }
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'Error during release: {str(e)}',
                'details': {'error': str(e)}
            }

    @property
    def total_items(self):
        """Get total number of items in this request"""
        if self.is_batch_request:
            return self.request_items.count()
        else:
            return 1 if self.item else 0

    @property
    def items_summary(self):
        """Get a summary of all items in this request"""
        if self.is_batch_request:
            items = self.request_items.all()
            if items.count() <= 3:
                return ", ".join([f"{item.item.name} ({item.quantity})" for item in items])
            else:
                first_three = items[:3]
                summary = ", ".join([f"{item.item.name} ({item.quantity})" for item in first_three])
                return f"{summary} and {items.count() - 3} more items"
        else:
            return f"{self.item.name} ({self.quantity})" if self.item else "No items"

    def __str__(self):
        if self.is_batch_request:
            return f"{self.request_id} - Batch Request ({self.total_items} items)"
        else:
            return f"{self.request_id} - {self.item.name} ({self.quantity})" if self.item else f"{self.request_id} - No items"

    class Meta:
        verbose_name = "Supply Request"
        verbose_name_plural = "Supply Requests"
        ordering = ['-created_at']


class SupplyRequestItem(models.Model):
    """Individual items within a supply request"""
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='request_items')
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])

    # Item-specific approval/release tracking
    approved_quantity = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(0)])
    released_quantity = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(0)])
    remarks = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.request.request_id} - {self.item.name} ({self.quantity})"

    class Meta:
        verbose_name = "Supply Request Item"
        verbose_name_plural = "Supply Request Items"
        unique_together = ['request', 'item']  # Prevent duplicate items in same request


class InventoryTransaction(models.Model):
    """Track all inventory movements"""
    TRANSACTION_TYPES = [
        ('IN', 'Stock In'),
        ('OUT', 'Stock Out'),
        ('ADJUSTMENT', 'Adjustment')
    ]
    
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()
    reference_request = models.ForeignKey(
        SupplyRequest, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='inventory_transactions'
    )
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    remarks = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.item.name} ({self.quantity})"
    
    class Meta:
        verbose_name = "Inventory Transaction"
        verbose_name_plural = "Inventory Transactions"
        ordering = ['-created_at']


class AuditLog(models.Model):
    """Model to track all system actions for audit purposes"""
    ACTION_TYPES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
        ('APPROVE', 'Approve'),
        ('REJECT', 'Reject'),
        ('RELEASE', 'Release'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('INVENTORY_ADJUST', 'Inventory Adjustment'),
        ('BULK_OPERATION', 'Bulk Operation'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    object_type = models.CharField(max_length=50)  # e.g., 'SupplyRequest', 'SupplyItem'
    object_id = models.CharField(max_length=50, null=True, blank=True)
    object_repr = models.CharField(max_length=200)  # String representation of the object
    changes = models.TextField(blank=True, default='{}')  # Store field changes as JSON string
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    additional_data = models.TextField(blank=True, default='{}')  # Any additional context as JSON string

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
            models.Index(fields=['object_type', 'object_id']),
        ]

    def __str__(self):
        user_str = self.user.username if self.user else 'System'
        return f"{user_str} - {self.action_type} - {self.object_type} - {self.timestamp}"

    def get_changes(self):
        """Get changes as a Python dict"""
        import json
        try:
            return json.loads(self.changes) if self.changes else {}
        except json.JSONDecodeError:
            return {}

    def set_changes(self, changes_dict):
        """Set changes from a Python dict"""
        import json
        self.changes = json.dumps(changes_dict) if changes_dict else '{}'

    def get_additional_data(self):
        """Get additional data as a Python dict"""
        import json
        try:
            return json.loads(self.additional_data) if self.additional_data else {}
        except json.JSONDecodeError:
            return {}

    def set_additional_data(self, data_dict):
        """Set additional data from a Python dict"""
        import json
        self.additional_data = json.dumps(data_dict) if data_dict else '{}'




# Signal to create UserProfile when User is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)
