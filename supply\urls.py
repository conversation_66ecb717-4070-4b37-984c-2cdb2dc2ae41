from django.urls import path
from . import views

app_name = 'supply'

urlpatterns = [
    path('', views.landing_page, name='landing_page'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('register/', views.register, name='register'),
    path('profile/', views.profile, name='profile'),
    path('gso/', views.gso_dashboard, name='gso_dashboard'),
    path('gso/requests/', views.gso_requests_dashboard, name='gso_requests_dashboard'),
    path('gso/requests/bulk-operations/', views.bulk_request_operations, name='bulk_request_operations'),
    path('gso/requests/<int:request_id>/', views.gso_request_detail, name='gso_request_detail'),
    path('gso/requests/<int:request_id>/approve/', views.approve_request, name='approve_request'),
    path('gso/requests/<int:request_id>/reject/', views.reject_request, name='reject_request'),

    # Release Management URLs
    path('gso/releases/', views.release_management, name='release_management'),
    path('gso/releases/<int:request_id>/', views.release_request, name='release_request'),
    path('gso/releases/bulk-operations/', views.bulk_release_operations, name='bulk_release_operations'),
    path('gso/releases/history/', views.release_history, name='release_history'),
    path('gso/releases/detail/<int:request_id>/', views.release_detail_view, name='release_detail_view'),

    path('gso/batch-operations/', views.batch_operations, name='batch_operations'),

    path('gso/batch-requests/<int:request_id>/', views.batch_request_detail, name='batch_request_detail'),

    # User Management URLs
    path('gso/users/', views.user_management, name='user_management'),
    path('gso/users/create/', views.user_create, name='user_create'),
    path('gso/users/<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('gso/users/<int:user_id>/toggle-status/', views.user_toggle_status, name='user_toggle_status'),
    path('gso/users/<int:user_id>/reset-password/', views.user_reset_password, name='user_reset_password'),
    path('gso/users/<int:user_id>/delete/', views.user_delete, name='user_delete'),

    # Supply Request URLs
    path('requests/', views.request_history, name='request_history'),
    path('requests/create/', views.request_create, name='request_create'),
    path('requests/create-batch/', views.request_create_batch, name='request_create_batch'),
    path('requests/search-items/', views.search_items, name='search_items'),
    path('requests/<int:request_id>/', views.request_detail, name='request_detail'),
    path('requests/item-info/', views.item_info, name='item_info'),
    path('requests/status-update/', views.request_status_update, name='request_status_update'),
    
    # Inventory URLs
    path('inventory/', views.inventory, name='inventory'),
    path('inventory/add/', views.inventory_add, name='inventory_add'),
    path('inventory/<int:item_id>/', views.inventory_detail, name='inventory_detail'),
    path('inventory/<int:item_id>/edit/', views.inventory_edit, name='inventory_edit'),
    path('inventory/<int:item_id>/adjust/', views.inventory_adjust, name='inventory_adjust'),
    path('inventory/<int:item_id>/delete/', views.inventory_delete, name='inventory_delete'),
    path('inventory/low-stock/', views.low_stock_alerts, name='low_stock_alerts'),
    path('inventory/transactions/', views.inventory_transactions, name='inventory_transactions'),

    # Category Management URLs
    path('categories/', views.category_management, name='category_management'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:category_id>/edit/', views.category_edit, name='category_edit'),
    path('categories/<int:category_id>/delete/', views.category_delete, name='category_delete'),

    # Reports URLs
    path('reports/', views.reports, name='reports'),
    path('reports/requests/', views.requests_report, name='requests_report'),
    path('reports/departmental/', views.departmental_usage_report, name='departmental_usage_report'),
    path('reports/inventory/', views.inventory_report, name='inventory_report'),

    # Search URLs
    path('search/', views.global_search, name='global_search'),

    # Dashboard widgets
    path('widgets/', views.dashboard_widgets, name='dashboard_widgets'),


]
