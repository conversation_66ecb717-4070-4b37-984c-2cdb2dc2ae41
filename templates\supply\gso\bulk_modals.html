<!-- Bulk Approval Modal -->
<div x-show="showBulkApproveModal" x-cloak 
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click.away="showBulkApproveModal = false">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Bulk Approve Requests</h3>
                <button @click="showBulkApproveModal = false" 
                        class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form method="post" action="{% url 'supply:bulk_request_operations' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="bulk_approve">
                
                <!-- Selected Requests -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">
                        You are about to approve <span x-text="selectedRequests.length" class="font-semibold"></span> request(s).
                    </p>
                    <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                        <template x-for="requestId in selectedRequests" :key="requestId">
                            <input type="hidden" name="selected_requests" :value="requestId">
                        </template>
                        <div class="text-xs text-gray-500">
                            Only pending requests with sufficient stock will be approved.
                        </div>
                    </div>
                </div>
                
                <!-- Approval Notes -->
                <div class="mb-4">
                    <label for="approval_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Approval Notes (Optional)
                    </label>
                    <textarea name="approval_notes" id="approval_notes" rows="3" 
                              placeholder="Enter any notes for this bulk approval..."
                              class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-sm"></textarea>
                </div>
                
                <!-- Actions -->
                <div class="flex items-center justify-end space-x-3">
                    <button type="button" @click="showBulkApproveModal = false" 
                            class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        Approve Selected
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Rejection Modal -->
<div x-show="showBulkRejectModal" x-cloak 
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click.away="showBulkRejectModal = false">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Bulk Reject Requests</h3>
                <button @click="showBulkRejectModal = false" 
                        class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form method="post" action="{% url 'supply:bulk_request_operations' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="bulk_reject">
                
                <!-- Selected Requests -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">
                        You are about to reject <span x-text="selectedRequests.length" class="font-semibold"></span> request(s).
                    </p>
                    <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                        <template x-for="requestId in selectedRequests" :key="requestId">
                            <input type="hidden" name="selected_requests" :value="requestId">
                        </template>
                        <div class="text-xs text-red-600">
                            This action cannot be undone. Only pending requests will be rejected.
                        </div>
                    </div>
                </div>
                
                <!-- Rejection Notes -->
                <div class="mb-4">
                    <label for="rejection_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason <span class="text-red-500">*</span>
                    </label>
                    <textarea name="rejection_notes" id="rejection_notes" rows="3" required
                              placeholder="Please provide a reason for rejecting these requests..."
                              class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm"></textarea>
                    <p class="text-xs text-gray-500 mt-1">Rejection reason is required and will be visible to requesters.</p>
                </div>
                
                <!-- Actions -->
                <div class="flex items-center justify-end space-x-3">
                    <button type="button" @click="showBulkRejectModal = false" 
                            class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        Reject Selected
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Release Modal -->
<div x-show="showBulkReleaseModal" x-cloak 
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click.away="showBulkReleaseModal = false">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Bulk Release Requests</h3>
                <button @click="showBulkReleaseModal = false" 
                        class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form method="post" action="{% url 'supply:bulk_request_operations' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="bulk_release">
                
                <!-- Selected Requests -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">
                        You are about to release <span x-text="selectedRequests.length" class="font-semibold"></span> request(s).
                    </p>
                    <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                        <template x-for="requestId in selectedRequests" :key="requestId">
                            <input type="hidden" name="selected_requests" :value="requestId">
                        </template>
                        <div class="text-xs text-gray-500">
                            Only approved requests with sufficient stock will be released.
                        </div>
                    </div>
                </div>
                
                <!-- Release Notes -->
                <div class="mb-4">
                    <label for="release_notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Release Notes (Optional)
                    </label>
                    <textarea name="release_notes" id="release_notes" rows="3" 
                              placeholder="Enter any notes for this bulk release..."
                              class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 sm:text-sm"></textarea>
                </div>
                
                <!-- Warning -->
                <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <div class="text-sm text-yellow-700">
                            <p class="font-medium">Important:</p>
                            <p>This will update inventory levels and mark requests as released. This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="flex items-center justify-end space-x-3">
                    <button type="button" @click="showBulkReleaseModal = false" 
                            class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                        Release Selected
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
