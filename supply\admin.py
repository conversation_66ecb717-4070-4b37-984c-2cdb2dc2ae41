from django.contrib import admin
from .models import UserProfile, SupplyItem, SupplyRequest, InventoryTransaction


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'department', 'created_at']
    list_filter = ['role', 'department']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'department']


@admin.register(SupplyItem)
class SupplyItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'current_stock', 'minimum_stock', 'unit', 'is_low_stock', 'updated_at']
    list_filter = ['unit', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'Low Stock'


@admin.register(SupplyRequest)
class SupplyRequestAdmin(admin.ModelAdmin):
    list_display = ['request_id', 'requester', 'department', 'item', 'quantity', 'status', 'created_at']
    list_filter = ['status', 'department', 'created_at']
    search_fields = ['request_id', 'requester__username', 'item__name', 'department']
    readonly_fields = ['request_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Request Information', {
            'fields': ('request_id', 'requester', 'department', 'item', 'quantity', 'purpose', 'status')
        }),
        ('Approval Information', {
            'fields': ('approved_by', 'approved_at', 'approval_remarks'),
            'classes': ('collapse',)
        }),
        ('Release Information', {
            'fields': ('released_by', 'released_at', 'release_remarks'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(InventoryTransaction)
class InventoryTransactionAdmin(admin.ModelAdmin):
    list_display = ['item', 'transaction_type', 'quantity', 'performed_by', 'created_at']
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['item__name', 'performed_by__username', 'remarks']
    readonly_fields = ['created_at']



