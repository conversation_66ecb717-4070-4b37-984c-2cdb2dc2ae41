{% extends 'base_new.html' %}

{% block title %}Release Management - MSRRMS{% endblock %}

{% block page_title %}Release Management{% endblock %}
{% block mobile_title %}Release Management{% endblock %}

{% block content %}
<div x-data="releaseManagement()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
                <h1 class="text-3xl font-bold">Release Management</h1>
                <p class="text-blue-100 mt-1">Process approved supply requests for release</p>
            </div>
            <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_approved }}</div>
                    <div class="text-blue-200 text-sm">Approved</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-300">{{ ready_for_release }}</div>
                    <div class="text-blue-200 text-sm">Ready to Release</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-300">{{ partial_release_available }}</div>
                    <div class="text-blue-200 text-sm">Partial Available</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex flex-wrap gap-3">
                <button @click="showBulkReleaseModal = true" 
                        :disabled="selectedRequests.length === 0"
                        :class="selectedRequests.length === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Release Selected (<span x-text="selectedRequests.length"></span>)
                </button>
                
                <a href="{% url 'supply:release_history' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Release History
                </a>
            </div>
            
            <div class="text-sm text-gray-600">
                <span x-show="selectedRequests.length > 0" x-text="selectedRequests.length + ' request(s) selected'"></span>
                <span x-show="selectedRequests.length === 0">Select requests to perform bulk operations</span>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <form hx-get="{% url 'supply:release_management' %}" 
              hx-target="#release-list" 
              hx-trigger="change, submit"
              hx-indicator="#filter-loading"
              class="space-y-4">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    {{ filter_form.department }}
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    {{ filter_form.search }}
                </div>
                
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    {{ filter_form.date_from }}
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    {{ filter_form.date_to }}
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div>
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        {{ filter_form.sort_by }}
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    
                    <a href="{% url 'supply:release_management' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        Clear
                    </a>
                </div>
            </div>
            
            <div id="filter-loading" class="htmx-indicator">
                <div class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-sm text-gray-600">Filtering...</span>
                </div>
            </div>
        </form>
    </div>

    <!-- Release List -->
    <div id="release-list">
        {% include 'supply/gso/release_list.html' %}
    </div>

    <!-- Bulk Release Modal -->
    <div x-show="showBulkReleaseModal" 
         x-cloak
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click.away="showBulkReleaseModal = false">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <form hx-post="{% url 'supply:bulk_release_operations' %}"
                  hx-target="#modal-container"
                  hx-swap="innerHTML"
                  hx-indicator="#bulk-release-loading">
                {% csrf_token %}
                <input type="hidden" name="selected_requests" :value="selectedRequests.join(',')">
                
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 text-center mb-4">Bulk Release Requests</h3>
                    
                    <div class="mb-4">
                        <label for="release_remarks" class="block text-sm font-medium text-gray-700 mb-2">Release Notes (Optional)</label>
                        <textarea name="release_remarks" 
                                  id="release_remarks"
                                  rows="3" 
                                  class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm"
                                  placeholder="Optional notes for all selected releases..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="allow_partial_release" value="true"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Allow partial release for items with insufficient stock</span>
                        </label>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-800">
                                    You are about to release <span x-text="selectedRequests.length"></span> request(s). 
                                    This action will update inventory levels and cannot be undone.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex gap-3">
                        <button type="submit" 
                                class="flex-1 bg-green-600 border border-transparent rounded-lg shadow-sm py-2 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <span id="bulk-release-loading" class="htmx-indicator">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                            Release Selected
                        </button>
                        <button type="button" 
                                @click="showBulkReleaseModal = false"
                                class="flex-1 bg-white border border-gray-300 rounded-lg shadow-sm py-2 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Container for HTMX responses -->
    <div id="modal-container"></div>
</div>

<script>
function releaseManagement() {
    return {
        selectedRequests: [],
        showBulkReleaseModal: false,
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll(requestIds) {
            if (this.selectedRequests.length === requestIds.length) {
                this.selectedRequests = [];
            } else {
                this.selectedRequests = [...requestIds];
            }
        },
        
        isSelected(requestId) {
            return this.selectedRequests.includes(requestId);
        }
    }
}
</script>
{% endblock %}
