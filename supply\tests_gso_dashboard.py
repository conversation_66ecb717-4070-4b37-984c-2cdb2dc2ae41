from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import UserProfile, SupplyItem, SupplyRequest, SupplyCategory


class GSORequestsDashboardTestCase(TestCase):
    """Test cases for the GSO Supply Requests Dashboard"""

    def setUp(self):
        """Set up test data"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            email='<EMAIL>',
            password='gsopass123'
        )
        # Update the automatically created profile
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'IT'
        self.gso_profile.save()

        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            email='<EMAIL>',
            password='deptpass123'
        )
        # Update the automatically created profile
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'Finance'
        self.dept_profile.save()

        # Create category
        self.category = SupplyCategory.objects.create(
            name='Office Supplies',
            description='General office supplies'
        )

        # Create supply items
        self.item1 = SupplyItem.objects.create(
            name='Printer Paper',
            description='A4 white paper',
            category=self.category,
            unit='reams',
            current_stock=100,
            minimum_stock=10
        )

        self.item2 = SupplyItem.objects.create(
            name='Pens',
            description='Blue ballpoint pens',
            category=self.category,
            unit='pieces',
            current_stock=5,
            minimum_stock=20
        )

        # Create supply requests
        self.request1 = SupplyRequest.objects.create(
            item=self.item1,
            quantity=10,
            requester=self.dept_user,
            purpose='Monthly office needs',
            status='PENDING'
        )

        self.request2 = SupplyRequest.objects.create(
            item=self.item2,
            quantity=50,
            requester=self.dept_user,
            purpose='Department supplies',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=timezone.now()
        )

        self.request3 = SupplyRequest.objects.create(
            item=self.item1,
            quantity=5,
            requester=self.dept_user,
            purpose='Urgent printing needs',
            status='RELEASED',
            approved_by=self.gso_user,
            approved_at=timezone.now() - timedelta(days=1),
            released_by=self.gso_user,
            released_at=timezone.now()
        )

        self.client = Client()

    def test_dashboard_access_gso_user(self):
        """Test that GSO users can access the dashboard"""
        self.client.login(username='gsouser', password='gsopass123')
        response = self.client.get(reverse('supply:gso_requests_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supply Requests Dashboard')
        self.assertContains(response, 'Total Requests')

    def test_dashboard_access_department_user(self):
        """Test that department users cannot access the dashboard"""
        self.client.login(username='deptuser', password='deptpass123')
        response = self.client.get(reverse('supply:gso_requests_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirected due to role restriction

    def test_dashboard_statistics(self):
        """Test that dashboard shows correct statistics"""
        self.client.login(username='gsouser', password='gsopass123')
        response = self.client.get(reverse('supply:gso_requests_dashboard'))
        
        # Check that statistics are displayed
        self.assertContains(response, '3')  # Total requests
        self.assertContains(response, '1')  # Pending requests
        self.assertContains(response, '1')  # Approved requests
        self.assertContains(response, '1')  # Released requests

    def test_filtering_by_status(self):
        """Test filtering requests by status"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Filter by pending status
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'status': 'PENDING'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request1.item.name)
        self.assertNotContains(response, self.request2.item.name)

    def test_filtering_by_department(self):
        """Test filtering requests by department"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Filter by Finance department
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'department': 'Finance'})
        self.assertEqual(response.status_code, 200)
        # Should show all requests since they're all from Finance department
        self.assertContains(response, self.request1.item.name)

    def test_filtering_by_category(self):
        """Test filtering requests by category"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Filter by category
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'category': str(self.category.id)})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.request1.item.name)

    def test_search_functionality(self):
        """Test search functionality"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Search for printer paper
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'search': 'Printer'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Printer Paper')
        self.assertNotContains(response, 'Pens')

    def test_date_filtering(self):
        """Test date range filtering"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Filter by today's date
        today = timezone.now().date()
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {
            'date_from': today.strftime('%Y-%m-%d'),
            'date_to': today.strftime('%Y-%m-%d'),
            'date_type': 'created'
        })
        self.assertEqual(response.status_code, 200)

    def test_sorting_functionality(self):
        """Test sorting functionality"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Sort by item name
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'sort': 'item__name'})
        self.assertEqual(response.status_code, 200)

    def test_htmx_requests(self):
        """Test HTMX partial template responses"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Make HTMX request
        response = self.client.get(
            reverse('supply:gso_requests_dashboard'),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should return partial template
        self.assertTemplateUsed(response, 'supply/gso/requests_list.html')

    def test_export_csv(self):
        """Test CSV export functionality"""
        self.client.login(username='gsouser', password='gsopass123')
        
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'export': 'csv'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])

    def test_bulk_operations_access(self):
        """Test bulk operations endpoint access"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Test bulk approve
        response = self.client.post(reverse('supply:bulk_request_operations'), {
            'action': 'bulk_approve',
            'selected_requests': [self.request1.id],
            'approval_notes': 'Bulk approval test'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after operation

    def test_pagination(self):
        """Test pagination functionality"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Test with page size
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'page_size': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Showing 1-1 of')

    def test_empty_state(self):
        """Test empty state when no requests match filters"""
        self.client.login(username='gsouser', password='gsopass123')
        
        # Filter by non-existent department
        response = self.client.get(reverse('supply:gso_requests_dashboard'), {'department': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No requests found')


class BulkOperationsTestCase(TestCase):
    """Test cases for bulk operations functionality"""

    def setUp(self):
        """Set up test data for bulk operations"""
        # Create GSO user
        self.gso_user = User.objects.create_user(
            username='gsouser',
            email='<EMAIL>',
            password='gsopass123'
        )
        self.gso_profile = UserProfile.objects.get(user=self.gso_user)
        self.gso_profile.role = 'GSO'
        self.gso_profile.department = 'IT'
        self.gso_profile.save()

        # Create department user
        self.dept_user = User.objects.create_user(
            username='deptuser',
            email='<EMAIL>',
            password='deptpass123'
        )
        self.dept_profile = UserProfile.objects.get(user=self.dept_user)
        self.dept_profile.role = 'DEPARTMENT'
        self.dept_profile.department = 'Finance'
        self.dept_profile.save()

        # Create supply item
        self.item = SupplyItem.objects.create(
            name='Test Item',
            description='Test item for bulk operations',
            unit='pieces',
            current_stock=100,
            minimum_stock=10
        )

        # Create multiple pending requests
        self.requests = []
        for i in range(3):
            request = SupplyRequest.objects.create(
                item=self.item,
                quantity=5,
                requester=self.dept_user,
                purpose=f'Test request {i+1}',
                status='PENDING'
            )
            self.requests.append(request)

        self.client = Client()

    def test_bulk_approve_valid_requests(self):
        """Test bulk approval of valid requests"""
        self.client.login(username='gsouser', password='gsopass123')
        
        request_ids = [req.id for req in self.requests]
        response = self.client.post(reverse('supply:bulk_request_operations'), {
            'action': 'bulk_approve',
            'selected_requests': request_ids,
            'approval_notes': 'Bulk approval test'
        })
        
        self.assertEqual(response.status_code, 302)
        
        # Check that requests were approved
        for request in self.requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'APPROVED')

    def test_bulk_reject_with_notes(self):
        """Test bulk rejection with required notes"""
        self.client.login(username='gsouser', password='gsopass123')
        
        request_ids = [req.id for req in self.requests]
        response = self.client.post(reverse('supply:bulk_request_operations'), {
            'action': 'bulk_reject',
            'selected_requests': request_ids,
            'rejection_notes': 'Not needed at this time'
        })
        
        self.assertEqual(response.status_code, 302)
        
        # Check that requests were rejected
        for request in self.requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'REJECTED')

    def test_bulk_operations_no_selection(self):
        """Test bulk operations with no requests selected"""
        self.client.login(username='gsouser', password='gsopass123')
        
        response = self.client.post(reverse('supply:bulk_request_operations'), {
            'action': 'bulk_approve',
            'selected_requests': [],
            'approval_notes': 'Test'
        })
        
        self.assertEqual(response.status_code, 302)
        # Should redirect with error message
