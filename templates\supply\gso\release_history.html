{% extends 'base_new.html' %}

{% block title %}Release History - MSRRMS{% endblock %}

{% block page_title %}Release History{% endblock %}
{% block mobile_title %}Release History{% endblock %}

{% block content %}
<div x-data="releaseHistory()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
                <h1 class="text-3xl font-bold">Release History</h1>
                <p class="text-blue-100 mt-1">View and search all released supply requests</p>
            </div>
            <div class="mt-4 lg:mt-0 flex flex-wrap gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_released }}</div>
                    <div class="text-blue-200 text-sm">Total Released</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-300">{{ today_released }}</div>
                    <div class="text-blue-200 text-sm">Today</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-300">{{ this_week_released }}</div>
                    <div class="text-blue-200 text-sm">This Week</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex flex-wrap gap-3">
                <a href="{% url 'supply:release_management' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Release Management
                </a>
                
                <button @click="exportData()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export History
                </button>
            </div>
            
            <div class="text-sm text-gray-600">
                Showing {{ requests.start_index|default:0 }} to {{ requests.end_index|default:0 }} of {{ requests.paginator.count|default:0 }} releases
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-xl shadow-lg p-6">
        <form hx-get="{% url 'supply:release_history' %}" 
              hx-target="#history-list" 
              hx-trigger="change, submit"
              hx-indicator="#filter-loading"
              class="space-y-4">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    {{ filter_form.department }}
                </div>
                
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    {{ filter_form.search }}
                </div>
                
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    {{ filter_form.date_from }}
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    {{ filter_form.date_to }}
                </div>
                
                <div>
                    <label for="released_by" class="block text-sm font-medium text-gray-700 mb-1">Released By</label>
                    {{ filter_form.released_by }}
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div>
                        <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        {{ filter_form.sort_by }}
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-medium text-white hover:bg-blue-700 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    
                    <a href="{% url 'supply:release_history' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                        Clear
                    </a>
                </div>
            </div>
            
            <div id="filter-loading" class="htmx-indicator">
                <div class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-sm text-gray-600">Filtering...</span>
                </div>
            </div>
        </form>
    </div>

    <!-- Release History List -->
    <div id="history-list">
        {% include 'supply/gso/release_history_list.html' %}
    </div>

    <!-- Modal Container for HTMX responses -->
    <div id="modal-container"></div>
</div>

<script>
function releaseHistory() {
    return {
        exportData() {
            // Get current filter parameters
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);
            
            // Add export parameter
            params.append('export', 'csv');
            
            // Create download link
            const url = '{% url "supply:release_history" %}?' + params.toString();
            window.open(url, '_blank');
        }
    }
}
</script>
{% endblock %}
