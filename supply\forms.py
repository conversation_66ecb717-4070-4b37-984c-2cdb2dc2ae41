from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import UserProfile, SupplyItem, InventoryTransaction, SupplyRequest, SupplyCategory


class UserRegistrationForm(UserCreationForm):
    """Custom user registration form with profile fields"""
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Last Name'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Email Address'
        })
    )
    role = forms.ChoiceField(
        choices=UserProfile.ROLE_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
        })
    )
    department = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Department'
        })
    )
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Phone Number (Optional)'
        })
    )

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply styling to inherited fields
        self.fields['username'].widget.attrs.update({
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Username'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Confirm Password'
        })

    def save(self, commit=True):
        user = super().save(commit=False)
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']
        
        if commit:
            user.save()
            # Update the user profile that was created by the signal
            profile = user.userprofile
            profile.role = self.cleaned_data['role']
            profile.department = self.cleaned_data['department']
            profile.phone = self.cleaned_data['phone']
            profile.save()
        
        return user


class UserProfileForm(forms.ModelForm):
    """Form for updating user profile information"""
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
        })
    )

    class Meta:
        model = UserProfile
        fields = ['department', 'phone']
        widgets = {
            'department': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if self.user:
            self.fields['first_name'].initial = self.user.first_name
            self.fields['last_name'].initial = self.user.last_name
            self.fields['email'].initial = self.user.email

    def save(self, commit=True):
        profile = super().save(commit=False)
        
        if self.user:
            self.user.first_name = self.cleaned_data['first_name']
            self.user.last_name = self.cleaned_data['last_name']
            self.user.email = self.cleaned_data['email']
            
            if commit:
                self.user.save()
                profile.save()
        
        return profile


class SupplyItemForm(forms.ModelForm):
    """Form for creating and updating supply items"""
    
    class Meta:
        model = SupplyItem
        fields = ['name', 'description', 'category', 'unit', 'current_stock', 'minimum_stock', 'unit_cost', 'supplier', 'location']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Item Name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Item Description',
                'rows': 3
            }),
            'unit': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'e.g., pieces, boxes, reams'
            }),
            'current_stock': forms.NumberInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'min': '0'
            }),
            'minimum_stock': forms.NumberInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'min': '0'
            }),
            'category': forms.Select(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'step': '0.01',
                'min': '0'
            }),
            'supplier': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Supplier Name'
            }),
            'location': forms.TextInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Storage Location'
            }),
        }


class StockAdjustmentForm(forms.ModelForm):
    """Form for adjusting inventory stock levels"""
    adjustment_type = forms.ChoiceField(
        choices=[('IN', 'Add Stock'), ('OUT', 'Remove Stock'), ('ADJUSTMENT', 'Adjustment')],
        widget=forms.Select(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm'
        })
    )
    
    class Meta:
        model = InventoryTransaction
        fields = ['quantity', 'remarks']
        widgets = {
            'quantity': forms.NumberInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'min': '1'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Reason for adjustment',
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.item = kwargs.pop('item', None)
        super().__init__(*args, **kwargs)
    
    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        adjustment_type = cleaned_data.get('adjustment_type')
        
        if adjustment_type == 'OUT' and self.item and quantity:
            if quantity > self.item.current_stock:
                raise forms.ValidationError(
                    f"Cannot remove {quantity} items. Only {self.item.current_stock} available in stock."
                )
        
        return cleaned_data

class SupplyRequestForm(forms.ModelForm):
    """Form for creating supply requests"""
    
    class Meta:
        model = SupplyRequest
        fields = ['item', 'quantity', 'purpose']
        widgets = {
            'item': forms.Select(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'hx-get': '/requests/item-info/',
                'hx-target': '#item-info',
                'hx-trigger': 'change'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'min': '1',
                'placeholder': 'Enter quantity needed'
            }),
            'purpose': forms.Textarea(attrs={
                'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                'placeholder': 'Describe the purpose for this request',
                'rows': 4
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Only show items that have stock available
        self.fields['item'].queryset = SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
        self.fields['item'].empty_label = "Select an item..."
    
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        item = self.cleaned_data.get('item')
        
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError("Ensure this value is greater than or equal to 1.")
        
        if item and quantity:
            if quantity > item.current_stock:
                raise forms.ValidationError(
                    f"Requested quantity ({quantity}) exceeds available stock ({item.current_stock})."
                )
        
        return quantity
    
    def clean_purpose(self):
        purpose = self.cleaned_data.get('purpose')
        if purpose and len(purpose.strip()) < 10:
            raise forms.ValidationError("Purpose must be at least 10 characters long.")
        return purpose
    
    def save(self, commit=True):
        request = super().save(commit=False)
        if self.user:
            request.requester = self.user
            # Department will be set automatically in the model's save method
        
        if commit:
            request.save()
        
        return request


class RequestFilterForm(forms.Form):
    """Form for filtering supply requests"""
    STATUS_CHOICES = [('', 'All Statuses')] + SupplyRequest.STATUS_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'hx-get': '',  # Will be set dynamically
            'hx-target': '#requests-list',
            'hx-trigger': 'change'
        })
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
            'placeholder': 'Search by item name or request ID...',
            'hx-get': '',  # Will be set dynamically
            'hx-target': '#requests-list',
            'hx-trigger': 'keyup changed delay:500ms'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial values from initial data
        if 'initial' in kwargs:
            initial_data = kwargs['initial']
            if 'status' in initial_data:
                self.fields['status'].initial = initial_data['status']
            if 'search' in initial_data:
                self.fields['search'].initial = initial_data['search']


class ItemSearchForm(forms.Form):
    """Form for searching and filtering supply items"""
    CATEGORY_CHOICES = [
        ('', 'All Categories'),
        ('Office Supplies', 'Office Supplies'),
        ('Computer Supplies', 'Computer Supplies'),
        ('Cleaning Supplies', 'Cleaning Supplies'),
        ('Medical Supplies', 'Medical Supplies'),
        ('Maintenance Supplies', 'Maintenance Supplies'),
        ('Furniture & Equipment', 'Furniture & Equipment'),
    ]

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Search items by name, description...',
            'hx-get': '/requests/search-items/',
            'hx-target': '#items-grid',
            'hx-trigger': 'keyup changed delay:300ms',
            'hx-include': '[name="category"], [name="in_stock_only"]'
        })
    )

    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'hx-get': '/requests/search-items/',
            'hx-target': '#items-grid',
            'hx-trigger': 'change',
            'hx-include': '[name="search"], [name="in_stock_only"]'
        })
    )

    in_stock_only = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded',
            'hx-get': '/requests/search-items/',
            'hx-target': '#items-grid',
            'hx-trigger': 'change',
            'hx-include': '[name="search"], [name="category"]'
        })
    )


class BatchRequestForm(forms.Form):
    """Form for creating batch supply requests"""
    purpose = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Describe the purpose for these supply requests...',
            'rows': 4
        }),
        help_text="Describe why you need these supplies (minimum 10 characters)"
    )

    save_as_draft = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
        })
    )

    def clean_purpose(self):
        purpose = self.cleaned_data.get('purpose')
        if purpose and len(purpose.strip()) < 10:
            raise forms.ValidationError("Purpose must be at least 10 characters long.")
        return purpose


class UserManagementForm(forms.ModelForm):
    """Form for creating and editing department users"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Enter password'
        }),
        required=False,
        help_text="Leave blank to keep current password when editing"
    )

    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Confirm password'
        }),
        required=False
    )

    department = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Enter department name'
        })
    )

    phone = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Enter phone number'
        }),
        required=False
    )

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'email', 'is_active']
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'Enter username'
            }),
            'first_name': forms.TextInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'Enter first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'Enter last name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'Enter email address'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            })
        }

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        confirm_password = cleaned_data.get('confirm_password')

        if password and password != confirm_password:
            raise forms.ValidationError("Passwords do not match.")

        return cleaned_data


class UserSearchForm(forms.Form):
    """Form for searching and filtering users"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Search by name, username, email, or department...'
        })
    )

    department = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Filter by department'
        })
    )

    status = forms.ChoiceField(
        choices=[
            ('', 'All Users'),
            ('active', 'Active Users'),
            ('inactive', 'Inactive Users')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )


class BatchRequestApprovalForm(forms.Form):
    """Form for approving/rejecting batch requests with individual item control"""
    action = forms.ChoiceField(
        choices=[
            ('approve_all', 'Approve All Items'),
            ('reject_all', 'Reject All Items'),
            ('individual', 'Individual Item Actions')
        ],
        widget=forms.RadioSelect(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300'
        })
    )

    remarks = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'rows': 3,
            'placeholder': 'Add remarks for this decision...'
        }),
        required=False
    )


class SupplyCategoryForm(forms.ModelForm):
    """Form for creating and editing supply categories"""

    class Meta:
        model = SupplyCategory
        fields = ['name', 'description', 'color_code', 'icon_class', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'Enter category name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'rows': 3,
                'placeholder': 'Enter category description'
            }),
            'color_code': forms.TextInput(attrs={
                'type': 'color',
                'class': 'h-10 w-20 border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200',
            }),
            'icon_class': forms.TextInput(attrs={
                'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
                'placeholder': 'e.g., fas fa-box, fas fa-laptop, fas fa-chair'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            })
        }


class CategoryFilterForm(forms.Form):
    """Form for filtering items by category"""
    category = forms.ModelChoiceField(
        queryset=SupplyCategory.objects.filter(is_active=True),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Search items...'
        })
    )

    stock_status = forms.ChoiceField(
        choices=[
            ('', 'All Items'),
            ('low_stock', 'Low Stock'),
            ('in_stock', 'In Stock'),
            ('out_of_stock', 'Out of Stock')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )


class ReleaseFilterForm(forms.Form):
    """Form for filtering supply requests ready for release"""

    department = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Filter by department...'
        })
    )

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Search by request ID, item, or requester...'
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )

    sort_by = forms.ChoiceField(
        choices=[
            ('-approved_at', 'Newest Approved'),
            ('approved_at', 'Oldest Approved'),
            ('department', 'Department A-Z'),
            ('-department', 'Department Z-A'),
            ('requester__last_name', 'Requester A-Z'),
            ('-requester__last_name', 'Requester Z-A'),
        ],
        required=False,
        initial='-approved_at',
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )


class ReleaseConfirmationForm(forms.Form):
    """Form for confirming individual supply request release"""

    release_remarks = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Optional release notes or special instructions...',
            'rows': 3
        })
    )

    confirm_release = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
        })
    )


class BulkReleaseForm(forms.Form):
    """Form for bulk release operations"""

    selected_requests = forms.CharField(
        widget=forms.HiddenInput()
    )

    release_remarks = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Optional release notes for all selected requests...',
            'rows': 3
        })
    )

    allow_partial_release = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
        })
    )


class ReleaseHistoryFilterForm(forms.Form):
    """Form for filtering release history"""

    department = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Filter by department...'
        })
    )

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Search by request ID, item, or requester...'
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )

    released_by = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm',
            'placeholder': 'Filter by GSO staff who released...'
        })
    )

    sort_by = forms.ChoiceField(
        choices=[
            ('-released_at', 'Newest Released'),
            ('released_at', 'Oldest Released'),
            ('department', 'Department A-Z'),
            ('-department', 'Department Z-A'),
            ('requester__last_name', 'Requester A-Z'),
            ('-requester__last_name', 'Requester Z-A'),
        ],
        required=False,
        initial='-released_at',
        widget=forms.Select(attrs={
            'class': 'block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm'
        })
    )


