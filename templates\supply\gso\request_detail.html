{% extends 'base_new.html' %}

{% block title %}Request {{ request.request_id }} - GSO Dashboard - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="requestDetail()">
    <div class="max-w-4xl mx-auto">
        <!-- Loading indicators -->
        <div id="approval-loading" class="htmx-indicator fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50">
            <div class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Approving request...
            </div>
        </div>
        
        <div id="rejection-loading" class="htmx-indicator fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg z-50">
            <div class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Rejecting request...
            </div>
        </div>
        

        
        <div id="main-content">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Request Details</h1>
                    <p class="mt-1 text-lg text-gray-600">{{ request.request_id }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'supply:gso_dashboard' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Status Banner -->
        <div class="mb-6">
            {% if request.status == 'PENDING' %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                This request is <strong>pending approval</strong>. 
                                {% if not inventory_available %}
                                    <span class="text-red-600 font-semibold">Warning: Insufficient inventory to fulfill this request.</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            {% elif request.status == 'APPROVED' %}
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                This request has been <strong>approved</strong> by {{ request.approved_by.get_full_name|default:request.approved_by.username }} 
                                on {{ request.approved_at|date:"M d, Y \a\t g:i A" }}.
                            </p>
                        </div>
                    </div>
                </div>
            {% elif request.status == 'REJECTED' %}
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">
                                This request was <strong>rejected</strong> by {{ request.approved_by.get_full_name|default:request.approved_by.username }} 
                                on {{ request.approved_at|date:"M d, Y \a\t g:i A" }}.
                            </p>
                        </div>
                    </div>
                </div>
            {% elif request.status == 'RELEASED' %}
                <div class="bg-green-50 border-l-4 border-green-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">
                                This request has been <strong>released</strong> by {{ request.released_by.get_full_name|default:request.released_by.username }} 
                                on {{ request.released_at|date:"M d, Y \a\t g:i A" }}.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Request Information -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Request Information</h3>
                        
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Request ID</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ request.request_id }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    {% if request.status == 'PENDING' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    {% elif request.status == 'APPROVED' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Approved
                                        </span>
                                    {% elif request.status == 'REJECTED' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    {% elif request.status == 'RELEASED' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Released
                                        </span>
                                    {% endif %}
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requester</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-600">
                                                    {{ request.requester.first_name|slice:":1" }}{{ request.requester.last_name|slice:":1" }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="font-medium">{{ request.requester.get_full_name|default:request.requester.username }}</div>
                                            <div class="text-gray-500">{{ request.requester.email }}</div>
                                        </div>
                                    </div>
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.department }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Date Submitted</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.created_at|date:"M d, Y \a\t g:i A" }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.updated_at|date:"M d, Y \a\t g:i A" }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Item Details -->
                <div class="bg-white shadow rounded-lg mt-6">
                    <div class="px-4 py-5 sm:p-6">
                        {% if request.is_batch_request %}
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Requested Items ({{ request.request_items.count }})</h3>
                        {% else %}
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Item Details</h3>
                        {% endif %}

                        {% if item_missing %}
                        <!-- Missing Item Warning -->
                        <div class="p-4 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Item No Longer Available</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>The requested item has been removed from the inventory system and is no longer available. This request cannot be processed.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <dl class="mt-4 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requested Quantity</dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">
                                    {{ request.quantity|default:"N/A" }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Item Unavailable
                                    </span>
                                </dd>
                            </div>
                        </dl>

                        {% else %}
                        {% if request.is_batch_request %}
                        <!-- Batch Request Items Display -->
                        <div class="space-y-4">
                            {% for request_item in request.request_items.all %}
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-gray-900">{{ request_item.item.name }}</h4>
                                        <p class="text-sm text-gray-500 mt-1">{{ request_item.item.description|default:"No description available" }}</p>

                                        <dl class="mt-3 grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-3">
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Requested</dt>
                                                <dd class="mt-1 text-sm font-semibold text-gray-900">
                                                    {{ request_item.quantity }} {{ request_item.item.unit }}
                                                </dd>
                                            </div>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Available</dt>
                                                <dd class="mt-1 text-sm font-semibold">
                                                    <span class="{% if request_item.item.current_stock >= request_item.quantity %}text-green-600{% else %}text-red-600{% endif %}">
                                                        {{ request_item.item.current_stock }} {{ request_item.item.unit }}
                                                    </span>
                                                </dd>
                                            </div>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                                <dd class="mt-1">
                                                    {% if request_item.item.current_stock >= request_item.quantity %}
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Available
                                                        </span>
                                                    {% else %}
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            Insufficient Stock
                                                        </span>
                                                    {% endif %}
                                                </dd>
                                            </div>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <!-- Single Item Display -->
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">{{ request.item.name }}</h4>
                                <p class="text-sm text-gray-500 mt-1">{{ request.item.description|default:"No description available" }}</p>
                        {% endif %}

                                <dl class="mt-4 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Requested Quantity</dt>
                                        <dd class="mt-1 text-lg font-semibold text-gray-900">
                                            {{ request.quantity }} {{ request.item.unit }}
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Available Stock</dt>
                                        <dd class="mt-1 text-lg font-semibold">
                                            <span class="{% if request.item.current_stock >= request.quantity %}text-green-600{% else %}text-red-600{% endif %}">
                                                {{ request.item.current_stock }} {{ request.item.unit }}
                                            </span>
                                        </dd>
                                    </div>

                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Unit</dt>
                                        <dd class="mt-1 text-lg font-semibold text-gray-900">{{ request.item.unit }}</dd>
                                    </div>
                                </dl>

                                {% if request.item.current_stock < request.quantity %}
                                <div class="mt-4 p-3 bg-red-50 rounded-md">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-red-800">Insufficient Stock</h3>
                                            <div class="mt-2 text-sm text-red-700">
                                                <p>The requested quantity ({{ request.quantity }}) exceeds available stock ({{ request.item.current_stock }}).</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Purpose -->
                <div class="bg-white shadow rounded-lg mt-6">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Purpose</h3>
                        <div class="prose prose-sm max-w-none">
                            <p class="text-gray-700">{{ request.purpose|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions and History -->
            <div class="lg:col-span-1">
                <!-- Actions -->
                {% if request.is_batch_request and can_approve %}
                    <!-- Batch Request Approval Actions -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Batch Request Actions</h3>

                            <div class="space-y-4">
                                <!-- Approve All Button -->
                                <form method="post" class="space-y-3">
                                    {% csrf_token %}
                                    <input type="hidden" name="action" value="approve_all">

                                    <div>
                                        <label for="remarks" class="block text-sm font-medium text-gray-700 mb-2">
                                            Approval Remarks (Optional)
                                        </label>
                                        <textarea name="remarks" id="remarks" rows="3"
                                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                                  placeholder="Enter any remarks for this approval..."></textarea>
                                    </div>

                                    <div class="flex space-x-3">
                                        <button type="submit"
                                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                            Approve All Items
                                        </button>
                                    </div>
                                </form>

                                <!-- Reject All Button -->
                                <form method="post" class="border-t pt-4">
                                    {% csrf_token %}
                                    <input type="hidden" name="action" value="reject_all">

                                    <div>
                                        <label for="reject_remarks" class="block text-sm font-medium text-gray-700 mb-2">
                                            Rejection Reason
                                        </label>
                                        <textarea name="remarks" id="reject_remarks" rows="3"
                                                  class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm"
                                                  placeholder="Enter reason for rejection..." required></textarea>
                                    </div>

                                    <div class="mt-3">
                                        <button type="submit"
                                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                onclick="return confirm('Are you sure you want to reject this entire batch request?')">
                                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                            Reject All Items
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                {% elif can_approve %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>

                        <div class="space-y-3">
                            {% if item_missing %}
                                <!-- Item Missing - Show rejection option only -->
                                <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">Item Unavailable</h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p>This request cannot be approved because the item is no longer available.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if can_approve %}
                                <button @click="showRejectModal = true"
                                        class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    Reject Request
                                </button>
                                {% endif %}
                            {% else %}
                                <!-- Normal Actions -->
                                {% if can_approve %}
                                    {% if inventory_available %}
                                        <button @click="showApproveModal = true"
                                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            Approve Request
                                        </button>
                                    {% else %}
                                        <button disabled
                                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            Insufficient Stock
                                        </button>
                                    {% endif %}

                                    <button @click="showRejectModal = true"
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                        Reject Request
                                    </button>
                                {% endif %}


                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Request History -->
                <div class="bg-white shadow rounded-lg mt-6">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Request History</h3>
                        
                        <div class="flow-root">
                            <ul class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-500">Request submitted by <span class="font-medium text-gray-900">{{ request.requester.get_full_name|default:request.requester.username }}</span></p>
                                                </div>
                                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                    {{ request.created_at|date:"M d, Y" }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                
                                {% if request.approved_at %}
                                <li>
                                    <div class="relative pb-8">
                                        {% if request.released_at %}
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        {% endif %}
                                        <div class="relative flex space-x-3">
                                            <div>
                                                {% if request.status == 'APPROVED' or request.status == 'RELEASED' %}
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                {% else %}
                                                <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                                {% endif %}
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-500">
                                                        Request {{ request.status|lower }} by 
                                                        <span class="font-medium text-gray-900">{{ request.approved_by.get_full_name|default:request.approved_by.username }}</span>
                                                    </p>
                                                    {% if request.approval_remarks %}
                                                    <p class="mt-1 text-sm text-gray-600 italic">"{{ request.approval_remarks }}"</p>
                                                    {% endif %}
                                                </div>
                                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                    {{ request.approved_at|date:"M d, Y" }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}
                                
                                {% if request.released_at %}
                                <li>
                                    <div class="relative">
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-500">
                                                        Supplies released by 
                                                        <span class="font-medium text-gray-900">{{ request.released_by.get_full_name|default:request.released_by.username }}</span>
                                                    </p>
                                                    {% if request.release_remarks %}
                                                    <p class="mt-1 text-sm text-gray-600 italic">"{{ request.release_remarks }}"</p>
                                                    {% endif %}
                                                </div>
                                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                    {{ request.released_at|date:"M d, Y" }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Modals -->
        <!-- Approve Modal -->
        <div x-show="showApproveModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showApproveModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <form hx-post="{% url 'supply:approve_request' request.id %}"
                      hx-target="#main-content"
                      hx-swap="innerHTML"
                      hx-indicator="#approval-loading">
                    {% csrf_token %}
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 text-center">Approve Request</h3>
                        <div class="mt-4">
                            <label for="approval_remarks" class="block text-sm font-medium text-gray-700">Remarks (Optional)</label>
                            <textarea name="approval_remarks" 
                                      id="approval_remarks"
                                      rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                      placeholder="Add any remarks for this approval..."></textarea>
                        </div>
                        <div class="flex justify-center space-x-4 px-4 py-3 mt-4">
                            <button type="submit"
                                    class="px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                Approve
                            </button>
                            <button type="button"
                                    @click="showApproveModal = false" 
                                    class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Reject Modal -->
        <div x-show="showRejectModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showRejectModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <form hx-post="{% url 'supply:reject_request' request.id %}"
                      hx-target="#main-content"
                      hx-swap="innerHTML"
                      hx-indicator="#rejection-loading">
                    {% csrf_token %}
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 text-center">Reject Request</h3>
                        <div class="mt-4">
                            <label for="rejection_remarks" class="block text-sm font-medium text-gray-700">Reason for Rejection *</label>
                            <textarea name="rejection_remarks" 
                                      id="rejection_remarks"
                                      rows="3" 
                                      required
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                      placeholder="Please provide a reason for rejecting this request..."></textarea>
                        </div>
                        <div class="flex justify-center space-x-4 px-4 py-3 mt-4">
                            <button type="submit"
                                    class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                Reject
                            </button>
                            <button type="button"
                                    @click="showRejectModal = false" 
                                    class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>


        </div>
    </div>
</div>

<script>
function requestDetail() {
    return {
        showApproveModal: false,
        showRejectModal: false,

    }
}
</script>
{% endblock %}