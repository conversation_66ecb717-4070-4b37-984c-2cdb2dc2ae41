<!-- Release Detail Modal -->
<div x-data="{ show: true }" 
     x-show="show" 
     x-cloak
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click.away="show = false; setTimeout(() => document.getElementById('modal-container').innerHTML = '', 300)">
    
    <div class="relative top-4 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-lg bg-white"
         @click.stop>
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
                <h3 class="text-lg font-medium text-gray-900">Release Request Details</h3>
                <p class="text-sm text-gray-500">{{ request.request_id }}</p>
            </div>
            <button @click="show = false; setTimeout(() => document.getElementById('modal-container').innerHTML = '', 300)"
                    class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="py-4 max-h-96 overflow-y-auto">
            <!-- Request Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Requester</label>
                        <div class="mt-1 text-sm text-gray-900">
                            {{ request.requester.get_full_name }}
                            {% if request.requester.userprofile.department %}
                                <span class="text-gray-500">({{ request.requester.userprofile.department }})</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Department</label>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ request.department }}
                            </span>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Purpose</label>
                        <div class="mt-1 text-sm text-gray-900">{{ request.purpose }}</div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Request Date</label>
                        <div class="mt-1 text-sm text-gray-900">{{ request.created_at|date:"F d, Y g:i A" }}</div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Approved By</label>
                        <div class="mt-1 text-sm text-gray-900">
                            {{ request.approved_by.get_full_name }}
                            <div class="text-xs text-gray-500">{{ request.approved_at|date:"F d, Y g:i A" }}</div>
                        </div>
                    </div>
                    
                    {% if request.approval_remarks %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Approval Remarks</label>
                        <div class="mt-1 text-sm text-gray-900">{{ request.approval_remarks }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Items Section -->
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Requested Items</h4>
                
                {% if request.is_batch_request %}
                    <!-- Batch Request Items -->
                    <div class="space-y-3">
                        {% for item in request.request_items.all %}
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">{{ item.item.name }}</div>
                                    <div class="text-xs text-gray-500">{{ item.item.description|default:"No description" }}</div>
                                    <div class="text-sm text-gray-700 mt-1">
                                        Requested: {{ item.quantity }} {{ item.item.unit }}
                                        {% if item.approved_quantity and item.approved_quantity != item.quantity %}
                                            | Approved: {{ item.approved_quantity }} {{ item.item.unit }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">
                                        Stock: {{ item.item.current_stock }} {{ item.item.unit }}
                                    </div>
                                    {% if item.item.can_fulfill_quantity:item.approved_quantity|default:item.quantity %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Available
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Insufficient
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <!-- Single Item Request -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900">{{ request.item.name }}</div>
                                <div class="text-xs text-gray-500">{{ request.item.description|default:"No description" }}</div>
                                <div class="text-sm text-gray-700 mt-1">
                                    Requested: {{ request.quantity }} {{ request.item.unit }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">
                                    Stock: {{ request.item.current_stock }} {{ request.item.unit }}
                                </div>
                                {% if request.item.can_fulfill_quantity:request.quantity %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Available
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Insufficient
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Stock Status Summary -->
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Release Status</h4>
                
                {% if can_release %}
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h5 class="text-sm font-medium text-green-800">Ready for Full Release</h5>
                                <p class="text-sm text-green-700">All requested items are available in sufficient quantities.</p>
                            </div>
                        </div>
                    </div>
                {% elif can_partial_release %}
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-yellow-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h5 class="text-sm font-medium text-yellow-800">Partial Release Available</h5>
                                <p class="text-sm text-yellow-700">Some items can be released, but others have insufficient stock.</p>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h5 class="text-sm font-medium text-red-800">Cannot Release</h5>
                                <p class="text-sm text-red-700">Insufficient stock for the requested items.</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Release Form -->
            {% if can_release or can_partial_release %}
            <div class="border-t border-gray-200 pt-4">
                <h4 class="text-md font-medium text-gray-900 mb-3">Release Request</h4>
                
                <form hx-post="{% url 'supply:release_request' request.id %}"
                      hx-target="#modal-container"
                      hx-swap="innerHTML"
                      hx-indicator="#release-loading">
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <label for="release_remarks" class="block text-sm font-medium text-gray-700 mb-2">
                            Release Notes (Optional)
                        </label>
                        <textarea name="release_remarks" 
                                  id="release_remarks"
                                  rows="3" 
                                  class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm"
                                  placeholder="Optional notes about this release..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="confirm_release" value="true" required
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">
                                I confirm that I want to release this request and update inventory levels
                            </span>
                        </label>
                    </div>
                    
                    {% if can_partial_release and not can_release %}
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="allow_partial_release" value="true"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">
                                Allow partial release for available items only
                            </span>
                        </label>
                    </div>
                    {% endif %}
                    
                    <div class="flex gap-3">
                        <button type="submit" 
                                class="flex-1 bg-green-600 border border-transparent rounded-lg shadow-sm py-2 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <span id="release-loading" class="htmx-indicator">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                            Release Request
                        </button>
                        <button type="button" 
                                @click="show = false; setTimeout(() => document.getElementById('modal-container').innerHTML = '', 300)"
                                class="flex-1 bg-white border border-gray-300 rounded-lg shadow-sm py-2 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}
        </div>
    </div>
</div>
