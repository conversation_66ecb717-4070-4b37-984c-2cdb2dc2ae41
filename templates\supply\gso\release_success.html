<!-- Release Success Modal -->
<div x-data="{ show: true }" 
     x-show="show" 
     x-cloak
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     @click.away="show = false; setTimeout(() => { htmx.ajax('GET', '{% url 'supply:release_management' %}', {target: '#release-list', swap: 'outerHTML'}); document.getElementById('modal-container').innerHTML = ''; }, 300)">
    
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-lg bg-white"
         @click.stop>
        
        <!-- Success Icon -->
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full mb-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>
        
        <!-- Success Message -->
        <div class="text-center mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Release Successful!</h3>
            <p class="text-sm text-gray-600">
                Request <span class="font-medium text-blue-600">{{ request.request_id }}</span> has been successfully released.
            </p>
        </div>
        
        <!-- Release Details -->
        {% if result.details %}
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-green-900 mb-3">Release Summary</h4>
            
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-green-700">Released to:</span>
                    <span class="font-medium text-green-900">{{ request.department }}</span>
                </div>
                
                <div class="flex justify-between text-sm">
                    <span class="text-green-700">Released by:</span>
                    <span class="font-medium text-green-900">{{ result.details.released_by }}</span>
                </div>
                
                <div class="flex justify-between text-sm">
                    <span class="text-green-700">Release time:</span>
                    <span class="font-medium text-green-900">{{ result.details.released_at|date:"M d, Y g:i A" }}</span>
                </div>
                
                {% if result.details.released_items %}
                <div class="pt-2 border-t border-green-200">
                    <div class="text-sm text-green-700 mb-2">Released Items:</div>
                    {% for item in result.details.released_items %}
                    <div class="flex justify-between text-sm">
                        <span class="text-green-700">{{ item.item }}</span>
                        <span class="font-medium text-green-900">{{ item.quantity }} {{ item.unit }}</span>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if result.details.partial_release %}
                <div class="pt-2 border-t border-green-200">
                    <div class="flex items-center text-sm text-yellow-700">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        Partial release completed
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
            <button type="button"
                    @click="show = false; setTimeout(() => { htmx.ajax('GET', '{% url 'supply:release_management' %}', {target: '#release-list', swap: 'outerHTML'}); document.getElementById('modal-container').innerHTML = ''; }, 300)"
                    class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Continue Releasing
            </button>
            
            <a href="{% url 'supply:release_history' %}" 
               class="flex-1 bg-white border border-gray-300 rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                View History
            </a>
        </div>
    </div>
</div>

<!-- Auto-refresh release list and close modal after 5 seconds -->
<script>
    setTimeout(() => {
        // Refresh the release list
        htmx.ajax('GET', '{% url 'supply:release_management' %}', {
            target: '#release-list',
            swap: 'outerHTML'
        });
        // Clear the modal
        document.getElementById('modal-container').innerHTML = '';
    }, 5000);
</script>
