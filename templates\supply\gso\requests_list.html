<!-- Supply Requests List Partial Template for HTMX Updates -->
<div id="requests-list" class="space-y-6">
    {% if page_obj %}
        <!-- List Header with Selection -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="select_all" 
                               @change="toggleAll($event.target.checked)"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 text-sm text-gray-700">Select All</label>
                    </div>
                    <div class="text-sm text-gray-600">
                        Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} request{{ page_obj.paginator.count|pluralize }}
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-600">Page Size:</label>
                    <select hx-get="{% url 'supply:gso_requests_dashboard' %}" 
                            hx-target="#requests-list" 
                            hx-include="form"
                            name="page_size"
                            class="text-sm border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="10" {% if page_size == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if page_size == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if page_size == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if page_size == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Requests Cards Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {% for request in page_obj %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                    <!-- Card Header -->
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3">
                                <input type="checkbox" name="request_checkbox" value="{{ request.id }}"
                                       @change="toggleRequest({{ request.id }})"
                                       :checked="isSelected({{ request.id }})"
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                        <a href="{% url 'supply:gso_request_detail' request.id %}" 
                                           class="hover:text-blue-600 transition-colors duration-200">
                                            {{ request.item.name }}
                                        </a>
                                    </h3>
                                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                                        <span class="font-medium">{{ request.request_id }}</span>
                                        <span class="text-gray-400">•</span>
                                        <span>Qty: {{ request.quantity }} {{ request.item.unit }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status Badge -->
                            <div class="ml-4">
                                {% if request.status == 'PENDING' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                {% elif request.status == 'APPROVED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Approved
                                    </span>
                                {% elif request.status == 'REJECTED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Rejected
                                    </span>
                                {% elif request.status == 'RELEASED' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Released
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Department and Requester -->
                        <div class="flex items-center space-x-4 mt-3">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <span class="text-xs text-gray-600">{{ request.requester.userprofile.department|default:"Unknown" }}</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="text-xs text-gray-600">{{ request.requester.get_full_name|default:request.requester.username }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Card Content -->
                    <div class="p-6">
                        <!-- Stock Information -->
                        {% if request.status == 'PENDING' or request.status == 'APPROVED' %}
                            <div class="mb-4">
                                <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                                    <span>Stock Available</span>
                                    <span>{{ request.item.current_stock }} {{ request.item.unit }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    {% with stock_percentage=request.item.current_stock requested=request.quantity %}
                                        {% if stock_percentage >= requested %}
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                        {% elif stock_percentage > 0 %}
                                            <div class="bg-yellow-600 h-2 rounded-full" style="width: {% widthratio stock_percentage requested 100 %}%"></div>
                                        {% else %}
                                            <div class="bg-red-600 h-2 rounded-full" style="width: 0%"></div>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                                <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                                    <span>Requested: {{ request.quantity }}</span>
                                    {% if request.item.current_stock >= request.quantity %}
                                        <span class="text-green-600 font-medium">✓ Available</span>
                                    {% elif request.item.current_stock > 0 %}
                                        <span class="text-yellow-600 font-medium">⚠ Partial</span>
                                    {% else %}
                                        <span class="text-red-600 font-medium">✗ Out of Stock</span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Request Details -->
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="text-gray-900">{{ request.created_at|date:"M d, Y" }}</span>
                            </div>
                            
                            {% if request.approved_at %}
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Approved:</span>
                                    <span class="text-gray-900">{{ request.approved_at|date:"M d, Y" }}</span>
                                </div>
                            {% endif %}
                            
                            {% if request.released_at %}
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Released:</span>
                                    <span class="text-gray-900">{{ request.released_at|date:"M d, Y" }}</span>
                                </div>
                            {% endif %}
                            
                            {% if request.approved_by %}
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Approved by:</span>
                                    <span class="text-gray-900">{{ request.approved_by.get_full_name|default:request.approved_by.username }}</span>
                                </div>
                            {% endif %}
                            
                            {% if request.item.category %}
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Category:</span>
                                    <span class="text-gray-900">{{ request.item.category.name }}</span>
                                </div>
                            {% endif %}
                            
                            {% if request.purpose %}
                                <div class="mt-3">
                                    <span class="text-gray-600 text-xs">Purpose:</span>
                                    <p class="text-gray-700 text-xs mt-1 line-clamp-2">{{ request.purpose|truncatewords:15 }}</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between">
                            <a href="{% url 'supply:gso_request_detail' request.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
                                View Details
                            </a>
                            
                            <div class="flex items-center space-x-2">
                                {% if request.status == 'PENDING' %}
                                    {% if request.item.current_stock >= request.quantity %}
                                        <button hx-post="{% url 'supply:approve_request' request.id %}"
                                                hx-target="#requests-list"
                                                hx-confirm="Are you sure you want to approve this request?"
                                                class="inline-flex items-center px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                            Approve
                                        </button>
                                    {% else %}
                                        <span class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-500 text-xs font-medium rounded-lg">
                                            Insufficient Stock
                                        </span>
                                    {% endif %}
                                    <button hx-post="{% url 'supply:reject_request' request.id %}"
                                            hx-target="#requests-list"
                                            hx-confirm="Are you sure you want to reject this request?"
                                            class="inline-flex items-center px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                        Reject
                                    </button>
                                {% elif request.status == 'APPROVED' %}
                                    {% if request.item.current_stock >= request.quantity %}
                                        <a href="{% url 'supply:release_request' request.id %}" 
                                           class="inline-flex items-center px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                            Release
                                        </a>
                                    {% elif request.item.current_stock > 0 %}
                                        <a href="{% url 'supply:release_request' request.id %}" 
                                           class="inline-flex items-center px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 text-xs font-medium rounded-lg transition-colors duration-200">
                                            Partial Release
                                        </a>
                                    {% else %}
                                        <span class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-500 text-xs font-medium rounded-lg">
                                            Out of Stock
                                        </span>
                                    {% endif %}
                                {% elif request.status == 'RELEASED' %}
                                    <span class="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-lg">
                                        ✓ Completed
                                    </span>
                                {% elif request.status == 'REJECTED' %}
                                    <span class="inline-flex items-center px-3 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-lg">
                                        ✗ Rejected
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
                <div class="flex flex-1 justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                           hx-get="{% url 'supply:gso_requests_dashboard' %}?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                           hx-target="#requests-list"
                           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                           hx-get="{% url 'supply:gso_requests_dashboard' %}?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                           hx-target="#requests-list"
                           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of
                            <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                                   hx-get="{% url 'supply:gso_requests_dashboard' %}?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                                   hx-target="#requests-list"
                                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Previous</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">{{ num }}</span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?page={{ num }}&{{ request.GET.urlencode }}"
                                       hx-get="{% url 'supply:gso_requests_dashboard' %}?page={{ num }}&{{ request.GET.urlencode }}"
                                       hx-target="#requests-list"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                                   hx-get="{% url 'supply:gso_requests_dashboard' %}?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                                   hx-target="#requests-list"
                                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    <span class="sr-only">Next</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No requests found</h3>
            <p class="mt-1 text-sm text-gray-500">No supply requests match the current filters.</p>
            <div class="mt-6">
                <button @click="clearFilters()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Clear Filters
                </button>
            </div>
        </div>
    {% endif %}
</div>
