{% extends 'base_new.html' %}

{% block title %}GSO Supply Requests Dashboard - MSRRMS{% endblock %}

{% block page_title %}GSO Supply Requests Dashboard{% endblock %}
{% block mobile_title %}Supply Requests{% endblock %}

{% block content %}
<div x-data="gsoRequestsDashboard()" class="space-y-6">
    <!-- Header Section with Statistics -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">Supply Requests Dashboard</h1>
                <p class="text-blue-100 mt-1">Comprehensive management of all departmental supply requests</p>
            </div>
            <div class="hidden lg:flex items-center space-x-6">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_requests }}</div>
                    <div class="text-blue-200 text-sm">Total Requests</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-200">{{ pending_requests }}</div>
                    <div class="text-blue-200 text-sm">Pending</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-200">{{ approved_requests }}</div>
                    <div class="text-blue-200 text-sm">Approved</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-200">{{ released_requests }}</div>
                    <div class="text-blue-200 text-sm">Released</div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Stats -->
        <div class="lg:hidden mt-4 grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-xl font-bold">{{ total_requests }}</div>
                <div class="text-blue-200 text-xs">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-yellow-200">{{ pending_requests }}</div>
                <div class="text-blue-200 text-xs">Pending</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-green-200">{{ approved_requests }}</div>
                <div class="text-blue-200 text-xs">Approved</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-purple-200">{{ released_requests }}</div>
                <div class="text-blue-200 text-xs">Released</div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">This Week</div>
                    <div class="text-2xl font-bold text-gray-900">{{ recent_requests }}</div>
                    <div class="text-sm text-green-600">New requests</div>
                </div>
            </div>
        </div>

        <!-- Recent Approvals -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">This Week</div>
                    <div class="text-2xl font-bold text-gray-900">{{ recent_approvals }}</div>
                    <div class="text-sm text-blue-600">Approvals</div>
                </div>
            </div>
        </div>

        <!-- Recent Releases -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-500">This Week</div>
                    <div class="text-2xl font-bold text-gray-900">{{ recent_releases }}</div>
                    <div class="text-sm text-purple-600">Releases</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div class="flex flex-wrap gap-2">
            <!-- Bulk Actions -->
            <div x-show="selectedRequests.length > 0" x-cloak class="flex flex-wrap gap-2">
                <button @click="showBulkApproveModal = true" 
                        class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Bulk Approve (<span x-text="selectedRequests.length"></span>)
                </button>
                <button @click="showBulkRejectModal = true" 
                        class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Bulk Reject (<span x-text="selectedRequests.length"></span>)
                </button>
                <button @click="showBulkReleaseModal = true" 
                        class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Bulk Release (<span x-text="selectedRequests.length"></span>)
                </button>
                <button @click="clearSelection()" 
                        class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    Clear Selection
                </button>
            </div>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <!-- Export Options -->
            <div class="relative" x-data="{ showExportMenu: false }">
                <button @click="showExportMenu = !showExportMenu" 
                        class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="showExportMenu" x-cloak @click.away="showExportMenu = false"
                     class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <div class="py-1">
                        <a href="?export=csv&{{ request.GET.urlencode }}" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            Export as CSV
                        </a>
                        <a href="?export=excel&{{ request.GET.urlencode }}" 
                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            Export as Excel
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Refresh Button -->
            <button @click="refreshData()" 
                    class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm font-medium rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Advanced Filters</h3>
            <button @click="showFilters = !showFilters" 
                    class="lg:hidden inline-flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors duration-200">
                <span x-text="showFilters ? 'Hide' : 'Show'"></span>
                <svg class="w-4 h-4 ml-1" :class="{'rotate-180': showFilters}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>
        
        <div x-show="showFilters" x-collapse class="lg:block">
            <form hx-get="{% url 'supply:gso_requests_dashboard' %}" 
                  hx-target="#requests-list" 
                  hx-trigger="change, keyup delay:500ms from:input[type=text], keyup delay:500ms from:input[type=search]"
                  hx-indicator="#loading-indicator"
                  class="space-y-4">
                
                <!-- First Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select name="status" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                            <option value="">All Status</option>
                            <option value="PENDING" {% if status_filter == 'PENDING' %}selected{% endif %}>Pending</option>
                            <option value="APPROVED" {% if status_filter == 'APPROVED' %}selected{% endif %}>Approved</option>
                            <option value="REJECTED" {% if status_filter == 'REJECTED' %}selected{% endif %}>Rejected</option>
                            <option value="RELEASED" {% if status_filter == 'RELEASED' %}selected{% endif %}>Released</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                        <select name="department" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                                <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id|stringformat:"s" == category_filter %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input type="search" name="search" value="{{ search_query }}" 
                               placeholder="Search requests, items, users..."
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                </div>
                
                <!-- Second Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date Type</label>
                        <select name="date_type" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                            <option value="created" {% if date_type == 'created' %}selected{% endif %}>Created Date</option>
                            <option value="approved" {% if date_type == 'approved' %}selected{% endif %}>Approved Date</option>
                            <option value="released" {% if date_type == 'released' %}selected{% endif %}>Released Date</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                        <input type="date" name="date_from" value="{{ date_from }}" 
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                        <input type="date" name="date_to" value="{{ date_to }}" 
                               class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select name="sort" class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 sm:text-sm">
                            <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>Created Date (Newest)</option>
                            <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Created Date (Oldest)</option>
                            <option value="-approved_at" {% if sort_by == '-approved_at' %}selected{% endif %}>Approved Date (Newest)</option>
                            <option value="approved_at" {% if sort_by == 'approved_at' %}selected{% endif %}>Approved Date (Oldest)</option>
                            <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item Name (A-Z)</option>
                            <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Name (Z-A)</option>
                            <option value="requester__userprofile__department" {% if sort_by == 'requester__userprofile__department' %}selected{% endif %}>Department (A-Z)</option>
                            <option value="-quantity" {% if sort_by == '-quantity' %}selected{% endif %}>Quantity (High to Low)</option>
                            <option value="quantity" {% if sort_by == 'quantity' %}selected{% endif %}>Quantity (Low to High)</option>
                            <option value="status" {% if sort_by == 'status' %}selected{% endif %}>Status</option>
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <button type="button" @click="clearFilters()" 
                                class="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                            Clear Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading requests...
        </div>
    </div>

    <!-- Requests List -->
    <div id="requests-list">
        {% include 'supply/gso/requests_list.html' %}
    </div>
</div>

<!-- Bulk Action Modals -->
{% include 'supply/gso/bulk_modals.html' %}

<script>
function gsoRequestsDashboard() {
    return {
        selectedRequests: [],
        showFilters: true,
        showBulkApproveModal: false,
        showBulkRejectModal: false,
        showBulkReleaseModal: false,
        
        init() {
            // Initialize on mobile
            if (window.innerWidth < 1024) {
                this.showFilters = false;
            }
            
            // Auto-refresh every 30 seconds
            setInterval(() => {
                this.refreshData();
            }, 30000);
        },
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll(checked) {
            if (checked) {
                // Select all visible requests
                const checkboxes = document.querySelectorAll('input[name="request_checkbox"]:not(:disabled)');
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            } else {
                this.selectedRequests = [];
            }
        },
        
        isSelected(requestId) {
            return this.selectedRequests.includes(requestId);
        },
        
        clearSelection() {
            this.selectedRequests = [];
            // Uncheck all checkboxes
            document.querySelectorAll('input[name="request_checkbox"]').forEach(cb => {
                cb.checked = false;
            });
            document.querySelector('input[name="select_all"]').checked = false;
        },
        
        clearFilters() {
            // Clear all form inputs
            document.querySelectorAll('form input, form select').forEach(input => {
                if (input.type === 'text' || input.type === 'search' || input.type === 'date') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            
            // Trigger refresh
            htmx.ajax('GET', '{% url "supply:gso_requests_dashboard" %}', {
                target: '#requests-list',
                swap: 'outerHTML'
            });
        },
        
        refreshData() {
            htmx.ajax('GET', '{% url "supply:gso_requests_dashboard" %}', {
                target: '#requests-list',
                swap: 'outerHTML'
            });
        }
    }
}
</script>
{% endblock %}
